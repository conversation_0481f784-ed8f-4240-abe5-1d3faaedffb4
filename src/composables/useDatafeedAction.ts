import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'

// 全局调用计数，避免多个datafeed实例导致计数重置
const globalSymbolCallCount = new Map()

// 清理特定币种的调用计数
const clearSymbolCallCount = (symbol?: string) => {
  if (symbol) {
    // 清理特定币种的所有时间周期
    const keysToDelete = []
    for (const key of globalSymbolCallCount.keys()) {
      if (key.startsWith(symbol + '-')) {
        keysToDelete.push(key)
      }
    }
    keysToDelete.forEach(key => globalSymbolCallCount.delete(key))
    console.log(`[TradingView] 清理币种 ${symbol} 的调用计数`)
  } else {
    // 清理所有
    globalSymbolCallCount.clear()
    console.log('[TradingView] 清理所有调用计数')
  }
}

export default function useDatafeedAction(info) {
  const dataCache = new Map()
  const CACHE_DURATION = 2 * 60 * 1000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineList, klineTicker, ticker } = storeToRefs(store)
  const resolutionMap: any = {
    1: '1m',
    // 3: '3m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    // '3D': '3day',
    '1W': '1w',
    '1M': '1M'
  }

  const resolutionReMap: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }
  const subMap: any = {}
  let rafId: number | null = null

  function formatSymbol (symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }
  let key = ''
  async function handleMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const { firstDataRequest } = periodParams

    if (firstDataRequest && resolution === '1M') {
      if (klineList.value.length && klineTicker.value.currentPeriod === '1M' &&
          klineTicker.value.currentPair === symbolInfo.fullName) {
        preObj.value = klineList.value[0]
        onHistoryCallback(klineList.value, {noData: klineList.value.length === 0})
        return true
      }

      const waitForWebSocketData = () => {
        return new Promise((resolve) => {
          let attempts = 0
          const maxAttempts = 20
          const checkInterval = 100

          const checkData = () => {
            attempts++
            if (klineList.value.length && klineTicker.value.currentPeriod === '1M' &&
                klineTicker.value.currentPair === symbolInfo.fullName) {
              preObj.value = klineList.value[0]
              onHistoryCallback(klineList.value, {noData: klineList.value.length === 0})
              resolve(true)
            } else if (attempts >= maxAttempts) {
              resolve(false)
            } else {
              setTimeout(checkData, checkInterval)
            }
          }

          setTimeout(checkData, checkInterval)
        })
      }

      const hasWebSocketData = await waitForWebSocketData()
      return hasWebSocketData
    }
    return false
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    console.log(`[TradingView] getBars被调用:`, {
      symbol: symbolInfo.fullName,
      resolution,
      periodParams,
      timestamp: new Date().toLocaleTimeString()
    })
    const currentSymbol = symbolInfo.fullName
    pair.value = currentSymbol
    interval.value = resolutionMap[resolution]
    key = `${currentSymbol}-${resolution}`
    const { from, to, firstDataRequest, countBack } = periodParams;  // `from` 和 `to` 用来确定需要获取的时间范围

    if (firstDataRequest && resolution !== '1M') {
      const monthlyKey = `${symbolInfo.fullName}-1M-first`
      if (dataCache.has(monthlyKey)) {
        dataCache.delete(monthlyKey)
      }
      if (klineTicker.value.currentPeriod === '1M') {
        klineList.value = []
        klineTicker.value = {}
      }
    }

    if (resolution === '1M') {
      const handled = await handleMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
      if (handled) {
        return
      }
    }

    fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
  }
  const forceRefresh = ref(false)

  const clearCache = (targetSymbol?: string) => {
    dataCache.clear()

    if (targetSymbol) {
      // 只清理特定币种的调用计数
      clearSymbolCallCount(targetSymbol)
    } else {
      // 清理所有调用计数
      clearSymbolCallCount()
    }

    lastCompleteBar.value = {}
    monthlySubscriptionCache = {
      key: null,
      subscription: null,
      pair: null
    }
    klineList.value = []
    klineTicker.value = {}
    preObj.value = {}

    Object.keys(subMap).forEach(key => {
      delete subMap[key]
    })

    if (rafId) {
      cancelAnimationFrame(rafId)
      rafId = null
    }
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    if (force) {
      clearCache()
    }
  }

  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    const cacheKey = `${symbol}-${resolution}-${firstDataRequest ? 'first' : from}`
    const cachedData = dataCache.get(cacheKey)
    const isMonthlyResolution = resolution === '1M'
    const cacheTimeout = isMonthlyResolution ? (firstDataRequest ? 60 * 1000 : 30 * 1000) : CACHE_DURATION

    const monthlyKey = `${symbol}-1M-first`
    const hasMonthlyCache = dataCache.has(monthlyKey)
    const isFromMonthlyToMinute = hasMonthlyCache && resolution === 1 && firstDataRequest
    const shouldForceRefresh = forceRefresh.value || (hasMonthlyCache && !isMonthlyResolution && firstDataRequest) || isFromMonthlyToMinute

    if (cachedData && (Date.now() - cachedData.timestamp < cacheTimeout) && firstDataRequest && !shouldForceRefresh) {
      onHistoryCallback(cachedData.data, { noData: cachedData.data.length === 0 })
      return
    }

    if (forceRefresh.value) {
      forceRefresh.value = false
    }

    try {
      const now = Date.now()

      // 获取当前symbol的调用次数
      const symbolKey = `${symbol}-${resolution}`
      const callCount = globalSymbolCallCount.get(symbolKey) || 0

      // 显示当前调用计数状态
      console.log(`[TradingView] 当前调用计数状态:`, Object.fromEntries(globalSymbolCallCount))

      // 根据调用次数决定limit值
      let requestLimit
      if (callCount === 0) {
        // 第一次调用，使用300
        requestLimit = 300
        globalSymbolCallCount.set(symbolKey, 1)
        console.log(`[TradingView] 第一次调用 ${symbolKey}, limit=${requestLimit}, firstDataRequest=${firstDataRequest}, countBack=${countBack}`)
      } else {
        // 第二次及以后的调用，使用20
        requestLimit = 20
        globalSymbolCallCount.set(symbolKey, callCount + 1)
        console.log(`[TradingView] 第${callCount + 1}次调用 ${symbolKey}, limit=${requestLimit}, firstDataRequest=${firstDataRequest}, countBack=${countBack}`)
      }

      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: resolutionMap[resolution],
        before: firstDataRequest ? now : preObj.value.time,
        limit: requestLimit,
        origin:1,
      })

      if (data) {
        const formattedData = data.e.map(item => {
          const time = Number(item[0])
          const open = Math.abs(Number(item[1]))
          const high = Math.abs(Number(item[2]))
          const low = Math.abs(Number(item[3]))
          const close = Math.abs(Number(item[4]))
          const volume = Math.abs(Number(item[5]))
          return { time, open, high, low, close, volume }
        })

        if (formattedData.length > 0) {
          preObj.value = formattedData[0]
          if (firstDataRequest) {
            dataCache.set(cacheKey, {
              data: formattedData,
              timestamp: now
            })
          }

          if (isMonthlyResolution && firstDataRequest && formattedData.length > 0) {
            klineList.value = formattedData
            klineTicker.value = {
              ...formattedData[formattedData.length - 1],
              currentPair: symbol,
              currentPeriod: '1M'
            }
          }
        }

        onHistoryCallback(formattedData, { noData: formattedData.length === 0 })
      } else {
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      onErrorCallback(error)
    }
  }
  let lastCompleteBar = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    if (interval.value !== '1M') return false

    const monthlyKey = `${pair.value}_#_1M`
    const last = (val1[pair.value] || {}).last

    let monthlySubscription = null
    let subscriptionKey = null

    if (monthlySubscriptionCache.pair === pair.value &&
        monthlySubscriptionCache.key &&
        subMap[monthlySubscriptionCache.key]) {
      monthlySubscription = monthlySubscriptionCache.subscription
      subscriptionKey = monthlySubscriptionCache.key
    } else {
      if (subMap[monthlyKey]) {
        monthlySubscription = subMap[monthlyKey]
        subscriptionKey = monthlyKey
      } else {
        Object.keys(subMap).forEach(key => {
          const sub = subMap[key]
          if (sub && sub.symbol && formatSymbol(sub.symbol) === pair.value &&
              sub.resolution && resolutionMap[sub.resolution] === '1M') {
            monthlySubscription = sub
            subscriptionKey = key
          }
        })
      }

      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: monthlySubscription,
        pair: pair.value
      }
    }

    if (monthlySubscription && last && val2 && val2.currentPair &&
        formatSymbol(monthlySubscription.symbol) === val2.currentPair &&
        val2.currentPeriod === '1M' && val2.time && val2.open !== undefined) {

      const resultVal = {
        time: Number(val2.time),
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        close: safeNumber(last),
        volume: safeNumber(val2.volume)
      }

      const monthlyStateKey = `${pair.value}_#_1M`
      lastCompleteBar.value[monthlyStateKey] = {
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        volume: safeNumber(val2.volume)
      }

      monthlySubscription.listen(resultVal)
      return true
    }

    return false
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    if (interval.value === '1M') {
      return
    }

    const key = `${pair.value}_#_${interval.value}`
    const last = (val1[pair.value] || {}).last

    if (subMap[key] && last && formatSymbol(subMap[key].symbol) === pair.value) {
      let resultVal

      if (val2 && val2.currentPair && val2.currentPeriod &&
          formatSymbol(subMap[key].symbol) === val2.currentPair &&
          interval.value === val2.currentPeriod) {
        resultVal = {
          time: Number(val2.time),
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }
        lastCompleteBar.value[key] = {
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }
      } else if (val2 && val2.currentPair && formatSymbol(subMap[key].symbol) === val2.currentPair) {
        const baseBar = lastCompleteBar.value[key] || {}
        const currentClose = safeNumber(last)
        resultVal = {
          time: Number(val2.time) || Date.now(),
          open: safeNumber(val2.open, baseBar.open || currentClose),
          high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
          low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
          close: currentClose,
          volume: safeNumber(val2.volume, baseBar.volume)
        }
      } else if (lastCompleteBar.value[key]) {
        const baseBar = lastCompleteBar.value[key]
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: baseBar.open,
          high: Math.max(baseBar.high, currentClose),
          low: Math.min(baseBar.low, currentClose),
          close: currentClose,
          volume: baseBar.volume
        }
      } else {
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: currentClose,
          high: currentClose,
          low: currentClose,
          close: currentClose,
          volume: 0
        }
      }

      if (rafId) {
        cancelAnimationFrame(rafId)
      }

      rafId = requestAnimationFrame(() => {
        if (subMap[key] && subMap[key].listen) {
          subMap[key].listen(resultVal)
        }
        rafId = null
      })
    }
  }, { deep: true })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    const subscriptionKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`

    if (subMap[subscriptionKey]) {
      delete subMap[subscriptionKey]
    }
    if (subMap[subscriberUID]) {
      const oldKey = subMap[subscriberUID]
      if (typeof oldKey === 'string' && subMap[oldKey]) {
        delete subMap[oldKey]
      }
      delete subMap[subscriberUID]
    }

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    }

    const currentKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`
    Object.keys(lastCompleteBar.value).forEach(key => {
      if (key !== currentKey) {
        delete lastCompleteBar.value[key]
      }
    })

    const subscription = {
      resolution,
      symbol: symbolInfo.fullName,
      listen: (newPriceData) => {
        try {
          if (pair.value !== symbolInfo.fullName || !newPriceData) {
            return
          }
          onRealtimeCallback(newPriceData)
        } catch (error) {
          console.error('Error in realtime callback:', error)
        }
      }
    }

    subMap[subscriptionKey] = subscription
    subMap[subscriberUID] = subscriptionKey

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: subscription,
        pair: symbolInfo.fullName
      }
    }
  }

  return {
    historyCallback: () => {},
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '/') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        timezone: ' ', // 时区
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      const subscriptionKey = subMap[subscriberUID]

      if (subscriptionKey && typeof subscriptionKey === 'string') {
        const subscription = subMap[subscriptionKey]
        if (subscription && subscription.resolution && resolutionMap[subscription.resolution] === '1M') {
          monthlySubscriptionCache = {
            key: null,
            subscription: null,
            pair: null
          }
        }
        delete subMap[subscriptionKey]
      }
      delete subMap[subscriberUID]
    },
    clearCache,
    setForceRefresh
  }
}
import { defineStore } from 'pinia'
import { getErrors, listMainCoins, getPairList, getCurrencyRateData } from '~/api/public'
import { assetsByCoin, assetByCoinList } from '~/api/tf'
import { getTradesList, getKlinesApi } from '~/api/order'
import { getPairSetting, getLandingPairsApi, getAddDownLink } from '~/api/public'
import { useI18n } from "vue-i18n"
import { cookies } from '~/utils/cookies'
import { socket, socketAllTicker } from '~/utils'
import SocketClass from '~/utils/socket.ts'
import { nextTick } from 'vue'

let localPairInfo = {}
if (process.client) {
  localPairInfo = JSON.parse(localStorage.getItem('pairInfo') || '{}')
  console.log(999999, localPairInfo)
}

export const commonStore = defineStore('publicCommon', () => {
  const errorMessages = ref({})
  const coinList = ref([])
  const currencyRate = ref({})
  const exchangeRate = ref({
    rate: 'USD',
    symbol: '$'
  })
  const pair = ref('')
  const depthsStore = ref({})
  const coinAssetListObj = ref({})
  const CoinAssetObj = ref({})
  const mainAssetObj = ref({})
  const tradeAssetObj = ref({})
  const posMapObj = ref({})
  const klineList = ref([])
  const klineTicker = ref({})
  const allPairList = ref([])
  const dealsObj = ref({})
  const allAsset = ref({})
  const marketsObj = ref({})
  const COLLATERALSymbol = ref({})
  const assetAllCoinMap = ref({})
  const ticker = ref({})
  const isChangeOrder = ref(true)
  const isChangeFutureOrder = ref(false)
  const isChangePosition = ref(false)
  const orderChangeObj = ref({})
  const isHideAssets = ref(false)
  const pairInfo = ref(localPairInfo)
  const priceScale = ref(0)
  const quantityScale = ref(0)
  const isPairDetail = ref(false)
  const landingPairs = ref({})
  const tradeArr = ref([])
  const downLoadInfo = ref({})
  const currentLang = useI18n().locale.value
  const changePair = (p) => {
    const router = useRouter()
    const nuxtApp = useNuxtApp()
    const lang = nuxtApp.$i18n.locale.value
    if (p.includes('_SWAP')) {
      pair.value = p
      nextTick(() => {
        window.history.replaceState({}, null, `/${lang}/future/${p}`)
      })
      router.currentRoute.value.params.pair = p
    } else {
      router.push(`/${lang}/exchange/${p}`)
    }
  }
  const getDownLoadInfo = async() => {
    const { data } = await getAddDownLink()
    if (data) {
      const arr = data.filter((item) => {
        return item.device_type * 1 === 3 || item.device_type * 1 === 4 // 3是IOS 4是安卓
      })
      let obj = {}
      arr.forEach((item) => {
        obj[item.device_type * 1] = item
      })
      downLoadInfo.value = obj
    }
  }
  const getLandingPairs = async() => {
    const { data } = await getLandingPairsApi()
    if (data) {
      let obj = {}
      data.forEach((item) => {
        obj[item.pair] = item
      })
      landingPairs.value = obj
    }
  }
  const setHideAssets = () => {
    isHideAssets.value = !isHideAssets.value
  }
  const getMessageError = async(lang) => {
    const { data } = await getErrors({
      lang: lang ? lang : currentLang
    })
    if (data) {
      errorMessages.value = data
    } else {
      try {
        let times = 0
        const timer = setInterval(async () => {
          if (times > 4) {
            clearInterval(timer)
            return
          }
          times++
          const {
            result: intervalData
          } = await getErrors({
            lang: currentLang
          })

          if (intervalData) {
            errorMessages.value = result
            clearInterval(timer)
          }
        }, 1000)
      } catch (err) {}
    }
  }
  const getCurrencyRate = async() => {
    const { data } = await getCurrencyRateData()
    if (data) {
      currencyRate.value = data
    } else {
      try {
        window.isDispatchCurrencyRate = false
      } catch (err) {}
    }
  }
  const switchExchangeRate = (data) => {
    exchangeRate.value = data
  }
  const getCoinList = async() => {
    const { data } = await listMainCoins()
    if (data) {
      coinList.value = data
    }
  }
  const getAllPairList = async() => {
    const { data } = await getPairList()
    if (data) {
      allPairList.value = data.spot.concat(data.contract)
    }
  }
  const getAssetByCoinList = async() => {
    const { data } = await assetByCoinList()
    if (data) {
      assetAllCoinMap.value = data
    }
  }
  const getAssetsByCoin = async() => {
    const { data } = await assetsByCoin()
    if (data) {
      allAsset.value = {
        'all': data.eq,
        'main': data.mainEq,
        'trade': data.tradeEq,
        'unprofit': data.posmap['USDT'].unprofit,
        'assetmap': data.assetmap
      }
      if (data.arr.length > 0) {
        data.arr.forEach((item) => {
          CoinAssetObj.value[item.asset] = item
        })
      } else {
        CoinAssetObj.value = {}
      }
      const mainArray = data.main
      if (mainArray.length > 0) {
        mainAssetObj.value = {}
        mainArray.forEach((item) => {
          mainAssetObj.value[item.asset] = item
        })
        coinAssetListObj.value['main'] = mainArray.map((item) => {
          item.icon_url = data.assetmap && data.assetmap[item.asset] && data.assetmap[item.asset].icon_url
          return item
        })
      } else {
        mainAssetObj.value = {}
        coinAssetListObj.value['main'] = [
          {
            icon_url: data.assetmap['USDT'].icon_url,
            asset: 'USDT',
            maxTransferOut: 0,
            asset_weight: 0,
            balance: 0,
            balanceUnify: 0,
            c: false,
            collateral: true,
            discount: 1,
            discountForFee: 1,
            discountForMargin: 1,
            eqbtc: 0,
            eqcny: 0,
            equsdt: 0,
            holds: 0,
            total: 0,
            usdtunify: 0,
            withdrawable: 0
          }
        ]
      }
      const tradeArray = data.trade
      if (tradeArray.length > 0) {
        tradeAssetObj.value = {}
        tradeArray.forEach((item) => {
          tradeAssetObj.value[item.asset] = item
        })
        coinAssetListObj.value['trade'] = tradeArray.map((item) => {
          item.icon_url = data.assetmap && data.assetmap[item.asset] && data.assetmap[item.asset].icon_url
          return item
        })
      } else {
        tradeAssetObj.value = {}
        coinAssetListObj.value['trade'] = tradeArray
      }
      tradeArr.value = tradeArray
      posMapObj.value = data.posmap
    }
  }
  const getPairDetail = async(type, pair) => {
    const { data } = await getPairSetting({
      all_spot: 1,
      all_cnt: 1
    })
    if (data) {
      pairInfo.value = data.map((v) => {
        v.price_scale = typeof Number(v.price_scale) === 'number' && !Number.isNaN(Number(v.price_scale)) ? Number(v.price_scale) : 2
        v.quantity_scale = typeof Number(v.quantity_scale) === 'number' && !Number.isNaN(Number(v.quantity_scale)) ? Number(v.quantity_scale) : 4
        return v
      }).reduce((acc, item) => {
        acc[item.symbol] = item
        return acc
      }, {})

      if (process.client) {
        localStorage.setItem('pairInfo', JSON.stringify(pairInfo.value))
      }
    }
  }
  const getDepthSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`]})
    const cb = (res) => {
      console.log(res.t, res.d.pair, 'dhduehduheudheuheheu')
      const convertToNumber = (value) => {
        return typeof value === 'string' ? parseFloat(value) : value;
      }
      if (res.t === 0) { // 全量
        const {
          asks,
          bids
        } = res.d
        const result = {
          pair: res.d.pair,
          asks,
          bids
        }
        console.log(res.d.pair, result, 'ppppppppppppppp')
        depthsStore.value = { [pair]: result }
      } else {
        const {
          add,
          del
        } = res.d
        if (add.asks && depthsStore.value[pair]) {
          add.asks.forEach((v) => {
            depthsStore.value[pair].asks[v.price] = v
          })
        }
        if (add.bids && depthsStore.value[pair]) {
          add.bids.forEach((v) => {
            depthsStore.value[pair].bids[v.price] = v
          })
        }
        if (del.asks && depthsStore.value[pair]) {
          del.asks.forEach((v) => {
            if (Object.values(depthsStore.value[pair].asks).length > 0) {
              delete depthsStore.value[pair].asks[v.price]
            }
          })
        }
        if (del.bids && depthsStore.value[pair]) {
          del.bids.forEach((v) => {
            if (Object.values(depthsStore.value[pair].bids).length > 0) {
              delete depthsStore.value[pair].bids[v.price]
            }
          })
        }
      }
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`, cb)
  }
  const transChartData = (obj) => {
    const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    const finalItem = {
      time: Number(obj[0])
    }
    obj.forEach((v, i) => {
      finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
    })
    return finalItem
  }
  const getKlineList = async(pair: any, time: any) => {
    const { data } = await getKlinesApi({
      symbol: pair,
      market: pair.includes('_SWAP') ? 'lpc' : 'spot',
      time_frame: time,
      limit: 1000
    })
    if (data) {
      klineList.value = data.e.map((item: any) => transChartData(item))
    }
  }
  const getKlineSocket = async(pair: any, time: any) => {
    return new Promise((resolve: Function, reject: Function) => {
      socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`]})
      const cb = (res) => {
        if (res.t === 0) {
          klineList.value = res.d
          if (res.d.length && time === '1M') {
            klineTicker.value = {
              ...res.d[res.d.length - 1],
              currentPair: res.stream.split('.')[1],
              currentPeriod: time,
            }
            resolve()
            return
          }
        }
        if (res.d.length) {
          klineTicker.value = {
            ...res.d[res.d.length - 1],
            currentPair: res.stream.split('.')[1],
            currentPeriod: time,
          }
        }
        resolve()
      }
      socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`, cb)
    })
  }
  const socketLogin = ref(null)
  const subLogin = () => {
    if (socketLogin.value && socketLogin.value.websocket) {
      socketLogin.value.destroy(); // 使用新增的销毁方法
      socketLogin.value = null;
    }
    socketLogin.value = new SocketClass(`wss://madex-user.tonetou.com`)
    const session_id = cookies.get('session_id_origin') || cookies.get('session_id')
    socketLogin.value.send({"method":"LOGIN","auth":{"sid": session_id}})
    const cb = (res) => { // 资产变化
      getAssetsByCoin()
    }
    const cbO = (res) => { // 订单变化
      if (res && res.data && res.data.product.includes('_SWAP')) {
        isChangeFutureOrder.value = true
      } else {
        isChangeOrder.value = true
      }
    }
    const cb1 = (res) => { // 仓位变化
      isChangePosition.value = true
    }
    socketLogin.value.on('account', cb)
    socketLogin.value.on('order', cbO)
    socketLogin.value.on('position', cb1)
  }
  const reConnectUser = (pair) => {
    if (pair.includes('_SWAP')) {
      isChangePosition.value = true
      isChangeFutureOrder.value = true
    } else {
      isChangeOrder.value = true
    }
    getAssetsByCoin()
  }
  const subTradesSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`]})
    const cb = (res) => {
      if (res.t === 0) {
        dealsObj.value[pair] = res.d
      } else {
        Object.values(res.d).forEach((item) => {
          dealsObj.value[pair][item.i] = item
        })
      }
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`, cb)
  }
  const subTickerSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`]})
    const cb = (res) => {
      ticker.value[res.data.product] = res.data
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`, cb)
  }
  const subAllTickerSocket = () => {
    socketAllTicker.send({"method":"SUBSCRIBE","params":["ALL.ticker"]})
    const cb = (res) => {
      res.data.forEach((item) => {
        marketsObj.value[item.product] = item
      })
    }
    socketAllTicker.on('ALL.ticker', cb)
  }
  const subCOLLATERALTickerSocket = () => {
    socketAllTicker.send({"method":"SUBSCRIBE","params":["COLLATERAL.ticker"]})
    const cb = (res) => {
      res.data.forEach((item) => {
        COLLATERALSymbol.value[item.symbol] = item
      })
    }
    socketAllTicker.on('COLLATERAL.ticker', cb)
  }
  const cancelCOLLATERALTickerSocket = () => {
    socket.send({"method":"UNSUBSCRIBE","params":["COLLATERAL.ticker"]})
  }
  const cancelAllTicker = () => {
    socket.send({"method":"UNSUBSCRIBE","params":["ALL.ticker"]})
  }
  const cancelKline = (pair: any, time: any) => {
    socket.send({"method":"UNSUBSCRIBE", "params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`]})
  }
  const cancelSocket = (pair: any) => {
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.info`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`]})
  }
  return {
    assetAllCoinMap,
    allAsset,
    coinAssetListObj,
    CoinAssetObj,
    mainAssetObj,
    tradeAssetObj,
    posMapObj,
    errorMessages,
    currencyRate,
    exchangeRate,
    coinList,
    depthsStore,
    ticker,
    klineList,
    klineTicker,
    allPairList,
    dealsObj,
    isChangeFutureOrder,
    isChangeOrder,
    isChangePosition,
    orderChangeObj,
    isHideAssets,
    marketsObj,
    pairInfo,
    priceScale,
    quantityScale,
    isPairDetail,
    landingPairs,
    tradeArr,
    COLLATERALSymbol,
    downLoadInfo,
    pair,
    subCOLLATERALTickerSocket,
    cancelCOLLATERALTickerSocket,
    getLandingPairs,
    getPairDetail,
    setHideAssets,
    switchExchangeRate,
    getCurrencyRate,
    getAssetByCoinList,
    getAllPairList,
    cancelKline,
    getKlineSocket,
    getMessageError,
    getCoinList,
    getAssetsByCoin,
    getDepthSocket,
    subLogin,
    subAllTickerSocket,
    cancelAllTicker,
    subTradesSocket,
    subTickerSocket,
    cancelSocket,
    getDownLoadInfo,
    reConnectUser,
    changePair
  }
})